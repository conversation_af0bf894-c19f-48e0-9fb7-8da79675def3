/**
 * 段落编码系统
 * 为内容的每个段落分配唯一编码，用于大纲与内容的映射关系
 */

export interface EncodedParagraph {
  id: string // 段落唯一编码
  content: string // 段落内容
  index: number // 段落在文档中的索引
  type: 'text' | 'heading' | 'list' | 'quote' | 'code' // 段落类型
  level?: number // 标题级别（仅对heading类型有效）
  wordCount: number // 字数统计
  hash: string // 内容哈希，用于快速比较
}

export interface OutlineMapping {
  outlineId: string // 大纲项ID
  title: string // 大纲标题
  paragraphIds: string[] // 对应的段落ID列表
  summary: string // 大纲总结
  level: number // 大纲层级
}

export class ParagraphEncoder {
  /**
   * 将文本内容编码为段落数组
   */
  encodeContent(content: string): EncodedParagraph[] {
    const paragraphs: EncodedParagraph[] = []
    
    // 按段落分割内容
    const rawParagraphs = content.split(/\n\s*\n/).filter(p => p.trim())
    
    rawParagraphs.forEach((paragraph, index) => {
      const trimmedContent = paragraph.trim()
      if (!trimmedContent) return
      
      const encodedParagraph: EncodedParagraph = {
        id: this.generateParagraphId(trimmedContent, index),
        content: trimmedContent,
        index,
        type: this.detectParagraphType(trimmedContent),
        wordCount: this.countWords(trimmedContent),
        hash: this.generateHash(trimmedContent)
      }
      
      // 如果是标题，检测级别
      if (encodedParagraph.type === 'heading') {
        encodedParagraph.level = this.detectHeadingLevel(trimmedContent)
      }
      
      paragraphs.push(encodedParagraph)
    })
    
    return paragraphs
  }
  
  /**
   * 生成段落唯一ID
   */
  private generateParagraphId(content: string, index: number): string {
    const timestamp = Date.now().toString(36)
    const contentHash = this.generateHash(content).substring(0, 8)
    return `para-${index}-${contentHash}-${timestamp}`
  }
  
  /**
   * 检测段落类型
   */
  private detectParagraphType(content: string): EncodedParagraph['type'] {
    // 检测Markdown标题
    if (content.match(/^#{1,6}\s+/)) {
      return 'heading'
    }
    
    // 检测列表
    if (content.match(/^[\s]*[-*+]\s+/) || content.match(/^[\s]*\d+\.\s+/)) {
      return 'list'
    }
    
    // 检测引用
    if (content.match(/^>\s+/)) {
      return 'quote'
    }
    
    // 检测代码块
    if (content.match(/^```/) || content.match(/^\s{4,}/)) {
      return 'code'
    }
    
    return 'text'
  }
  
  /**
   * 检测标题级别
   */
  private detectHeadingLevel(content: string): number {
    const match = content.match(/^(#{1,6})\s+/)
    return match ? match[1].length : 1
  }
  
  /**
   * 统计字数（支持中英文）
   */
  private countWords(content: string): number {
    // 移除Markdown标记
    const cleanContent = content.replace(/[#*`>\-+]/g, '').trim()
    
    // 中文字符数
    const chineseChars = (cleanContent.match(/[\u4e00-\u9fff]/g) || []).length
    
    // 英文单词数
    const englishWords = cleanContent
      .replace(/[\u4e00-\u9fff]/g, '') // 移除中文字符
      .split(/\s+/)
      .filter(word => word.trim().length > 0).length
    
    return chineseChars + englishWords
  }
  
  /**
   * 生成内容哈希
   */
  private generateHash(content: string): string {
    let hash = 0
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash).toString(36)
  }
  
  /**
   * 将编码后的段落转换为用于AI处理的格式
   */
  formatForAI(paragraphs: EncodedParagraph[]): string {
    return paragraphs.map(p => {
      const typePrefix = p.type === 'heading' ? `[H${p.level || 1}]` : 
                        p.type === 'list' ? '[LIST]' :
                        p.type === 'quote' ? '[QUOTE]' :
                        p.type === 'code' ? '[CODE]' : '[TEXT]'
      
      return `${typePrefix}[ID:${p.id}] ${p.content}`
    }).join('\n\n')
  }
  
  /**
   * 从AI返回的内容中解析段落映射
   */
  parseAIResponse(aiResponse: string): OutlineMapping[] {
    const mappings: OutlineMapping[] = []
    
    // 尝试解析JSON格式的响应
    try {
      const parsed = JSON.parse(aiResponse)
      if (Array.isArray(parsed)) {
        return parsed.map((item, index) => ({
          outlineId: item.id || `outline-${index}`,
          title: item.title || `标题 ${index + 1}`,
          paragraphIds: item.paragraphIds || [],
          summary: item.summary || item.title || '',
          level: Math.max(1, Math.min(4, item.level || 1))
        }))
      }
    } catch (error) {
      console.warn('解析AI响应JSON失败，尝试文本解析:', error)
    }
    
    // 文本格式解析（备用方案）
    const lines = aiResponse.split('\n').filter(line => line.trim())
    
    lines.forEach((line, index) => {
      // 查找段落ID引用
      const idMatches = line.match(/\[ID:([^\]]+)\]/g)
      const paragraphIds = idMatches ? 
        idMatches.map(match => match.replace(/\[ID:([^\]]+)\]/, '$1')) : []
      
      // 提取标题
      const title = line.replace(/\[ID:[^\]]+\]/g, '').trim()
      
      if (title && title.length > 0) {
        mappings.push({
          outlineId: `outline-${index}`,
          title,
          paragraphIds,
          summary: title,
          level: 1
        })
      }
    })
    
    return mappings
  }
  
  /**
   * 根据段落ID查找段落内容
   */
  findParagraphById(paragraphs: EncodedParagraph[], id: string): EncodedParagraph | undefined {
    return paragraphs.find(p => p.id === id)
  }
  
  /**
   * 获取大纲项对应的所有段落内容
   */
  getOutlineContent(paragraphs: EncodedParagraph[], mapping: OutlineMapping): string {
    const relatedParagraphs = mapping.paragraphIds
      .map(id => this.findParagraphById(paragraphs, id))
      .filter(p => p !== undefined) as EncodedParagraph[]
    
    return relatedParagraphs.map(p => p.content).join('\n\n')
  }
}

// 导出单例实例
export const paragraphEncoder = new ParagraphEncoder()
