'use client'

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { cn } from '@/lib/utils'
import { List } from 'lucide-react'

interface OutlineItem {
  id: string
  title: string
  level: number
  paragraphIds?: string[]
  summary?: string
}

interface OutlineCardProps {
  outline: OutlineItem[]
  onHeadingClick: (headingId: string) => void
  isLoading?: boolean
}

const OutlineCard: React.FC<OutlineCardProps> = ({ outline, onHeadingClick, isLoading = false }) => {
  const [activeHeading, setActiveHeading] = useState<string | null>(null)
  const [visibleCount, setVisibleCount] = useState(0)
  const [cardHeight, setCardHeight] = useState<string>('auto')
  const [topOffset, setTopOffset] = useState<number>(24)
  const contentRef = useRef<HTMLDivElement>(null)
  const streamingTimerRef = useRef<NodeJS.Timeout | null>(null)
  const isStreamingCompleteRef = useRef<boolean>(false)

  // 优化的流式显示逻辑 - 修复闪烁问题
  useEffect(() => {
    // 清除之前的定时器
    if (streamingTimerRef.current) {
      clearTimeout(streamingTimerRef.current)
      streamingTimerRef.current = null
    }

    if (outline.length === 0) {
      setVisibleCount(0)
      isStreamingCompleteRef.current = false
      return
    }

    // 如果流式显示已完成，直接显示所有项目，避免重复动画
    if (isStreamingCompleteRef.current || visibleCount >= outline.length) {
      setVisibleCount(outline.length)
      isStreamingCompleteRef.current = true
      return
    }

    // 只在首次加载或新增内容时进行流式显示
    if (visibleCount < outline.length) {
      streamingTimerRef.current = setTimeout(() => {
        setVisibleCount(prev => {
          const newCount = prev + 1
          if (newCount >= outline.length) {
            isStreamingCompleteRef.current = true
          }
          return newCount
        })
      }, 150)
    }

    return () => {
      if (streamingTimerRef.current) {
        clearTimeout(streamingTimerRef.current)
        streamingTimerRef.current = null
      }
    }
  }, [outline.length, visibleCount])

  // 当大纲重新生成时，重置流式显示状态
  useEffect(() => {
    if (outline.length > 0) {
      // 如果是全新的大纲（长度变化），重置流式显示
      if (visibleCount === 0 || outline.length !== visibleCount) {
        isStreamingCompleteRef.current = false
        setVisibleCount(1) // 立即显示第一个条目
      }
    }
  }, [outline.length])

  // 优化的高度计算 - 实现垂直居中对称布局
  const calculateOptimalHeight = useCallback(() => {
    const viewportHeight = window.innerHeight

    // 计算父容器的padding（左侧大纲区域的padding）
    const parentPadding = 48 // p-6 = 1.5rem * 2 = 48px

    // 计算可用的垂直空间
    const availableHeight = viewportHeight - parentPadding

    // 设置最小和最大高度限制
    const minHeight = 400
    const maxHeight = availableHeight * 0.85 // 留出15%的空间作为缓冲

    // 计算理想高度（占可用空间的75%）
    const idealHeight = availableHeight * 0.75

    // 确保高度在合理范围内
    const finalHeight = Math.min(maxHeight, Math.max(minHeight, idealHeight))

    // 计算垂直居中的顶部偏移量
    // 使上下距离相等：(viewportHeight - finalHeight) / 2
    const centeredTopOffset = Math.max(24, (viewportHeight - finalHeight) / 2)

    setCardHeight(`${finalHeight}px`)
    setTopOffset(centeredTopOffset)
  }, [])

  // 初始化高度计算 - 只在组件挂载和窗口大小变化时执行
  useEffect(() => {
    // 初始计算
    calculateOptimalHeight()

    // 监听窗口大小变化
    window.addEventListener('resize', calculateOptimalHeight)

    return () => {
      window.removeEventListener('resize', calculateOptimalHeight)
    }
  }, [calculateOptimalHeight])

  const handleHeadingClick = useCallback((headingId: string) => {
    setActiveHeading(headingId)
    onHeadingClick(headingId)
  }, [onHeadingClick])

  const getIndentClass = (level: number) => {
    switch (level) {
      case 1: return 'pl-0'
      case 2: return 'pl-4'
      case 3: return 'pl-8'
      case 4: return 'pl-12'
      case 5: return 'pl-16'
      case 6: return 'pl-20'
      default: return 'pl-0'
    }
  }

  const getFontSizeClass = (level: number) => {
    switch (level) {
      case 1: return 'text-lg font-bold'
      case 2: return 'text-base font-semibold'
      case 3: return 'text-sm font-medium'
      case 4: return 'text-sm'
      case 5: return 'text-xs'
      case 6: return 'text-xs'
      default: return 'text-sm'
    }
  }

  // 确保在流式输出过程中始终显示卡片
  if (outline.length === 0 && !isLoading) return null



  return (
    <div className="sticky" style={{ top: `${topOffset}px`, height: cardHeight, zIndex: 50 }}>
      {/* 毛玻璃效果的大圆角卡片 */}
      <div className="backdrop-blur-xl bg-white/95 border border-gray-200/50 rounded-3xl shadow-2xl overflow-hidden ring-1 ring-white/30 h-full flex flex-col">
        {/* 大纲标题 */}
        <div className="px-6 py-4 border-b border-gray-200/30">
          <div className="flex items-center gap-2">
            <List className="w-5 h-5 text-blue-600" />
            <h3 className="text-lg font-semibold text-gray-900">智能大纲</h3>
          </div>
        </div>

        {/* 大纲内容 */}
        <div ref={contentRef} className="p-4 overflow-y-auto flex-1">
          {/* 始终显示大纲容器，避免布局跳动 */}
          <div className="space-y-1 min-h-[200px]">
            {isLoading && outline.length === 0 ? (
              <div className="flex items-center justify-center py-8">
                <div className="flex items-center gap-3">
                  <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  <span className="text-sm text-gray-600">正在生成智能大纲...</span>
                </div>
              </div>
            ) : (
              <>
                {/* 显示已生成的大纲项目 */}
                {outline.slice(0, Math.max(visibleCount, outline.length)).map((item, index) => {
                  // 只对正在流式显示的项目应用动画，已完成的项目直接显示
                  const shouldAnimate = !isStreamingCompleteRef.current && index >= visibleCount - 1

                  return (
                    <div
                      key={`${item.id}-${index}`}
                      className={cn(
                        "transition-all duration-300",
                        shouldAnimate ? "opacity-100 transform translate-x-0" : "opacity-100"
                      )}
                      style={shouldAnimate ? {
                        animationDelay: `${(index - (visibleCount - 1)) * 50}ms`,
                        animationFillMode: 'forwards'
                      } : {}}
                    >
                      <button
                        onClick={() => handleHeadingClick(item.id)}
                        className={cn(
                          "w-full text-left py-3 px-4 rounded-xl transition-all duration-300 hover:bg-blue-50/70 hover:shadow-sm group relative",
                          getIndentClass(item.level),
                          activeHeading === item.id
                            ? "bg-blue-100/70 text-blue-700 border-l-4 border-blue-500 shadow-sm"
                            : "text-gray-700 hover:text-gray-900 border-l-4 border-transparent"
                        )}
                      >
                        <div className={cn(
                          "transition-all duration-200",
                          getFontSizeClass(item.level),
                          activeHeading === item.id ? "font-semibold" : ""
                        )}>
                          <div className="line-clamp-2 mb-1">
                            {item.title}
                          </div>
                          {/* 显示总结信息（如果有且与标题不同） */}
                          {item.summary && item.summary !== item.title && (
                            <div className="text-xs text-gray-500 line-clamp-2 mt-1">
                              {item.summary}
                            </div>
                          )}
                          {/* 显示段落数量（如果有段落映射） */}
                          {item.paragraphIds && item.paragraphIds.length > 0 && (
                            <div className="text-xs text-blue-500 mt-1">
                              涵盖 {item.paragraphIds.length} 个段落
                            </div>
                          )}
                        </div>
                        {item.level === 1 && (
                          <div className="w-full h-px bg-gradient-to-r from-gray-200 to-transparent mt-2"></div>
                        )}
                        {/* 悬停指示器 */}
                        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                        </div>
                      </button>
                    </div>
                  )
                })}

                {/* 流式输出过程中的加载指示器 */}
                {isLoading && outline.length > 0 && visibleCount < outline.length && (
                  <div className="flex items-center gap-2 py-2 px-3 text-gray-500">
                    <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin"></div>
                    <span className="text-xs">正在加载更多...</span>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* 卡片底部装饰 */}
        <div className="h-1 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500"></div>
      </div>
    </div>
  )
}

export default OutlineCard
