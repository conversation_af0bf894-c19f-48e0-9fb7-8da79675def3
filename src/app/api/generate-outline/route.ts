import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { detectContentType, ContentType } from '@/lib/content-detector'
import { paragraphEncoder, type OutlineMapping } from '@/lib/paragraphEncoder'

// 初始化OpenAI客户端
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

/**
 * 为文本内容生成智能大纲（基于段落编码）
 * @param content 文本内容
 * @param title 文章标题
 * @returns 生成的大纲结构
 */
async function generateOutlineForContent(content: string, title: string): Promise<Array<{id: string, title: string, level: number}>> {
  // 检查是否有有效的OpenAI API密钥
  const apiKey = process.env.OPENAI_API_KEY
  if (!apiKey || apiKey === 'sk-test-key-for-development' || apiKey === 'your_openai_api_key_here') {
    // 返回基于简单解析的大纲
    return generateFallbackOutline(content)
  }

  try {
    // 使用段落编码系统
    const encodedParagraphs = paragraphEncoder.encodeContent(content)
    const formattedContent = paragraphEncoder.formatForAI(encodedParagraphs)

    const systemPrompt = `你是一个专业的内容分析师，擅长为文章生成清晰的大纲结构。

请分析用户提供的已编码内容，生成一个层次分明的大纲。要求：

1. 理解每个段落的内容和类型（通过[TYPE]和[ID:xxx]标记）
2. 生成总结性的大纲，而不是简单复制原文标题
3. 一条大纲应该对应多个相关段落，体现内容的逻辑关系
4. 确保大纲层次清晰（1-4级标题）
5. 返回JSON格式，包含id、title、level、paragraphIds、summary字段

返回格式示例：
[
  {
    "id": "introduction",
    "title": "核心概念介绍",
    "level": 1,
    "paragraphIds": ["para-0-abc123-xyz", "para-1-def456-xyz"],
    "summary": "介绍文章的主要概念和背景信息"
  },
  {
    "id": "main-analysis",
    "title": "深入分析与应用",
    "level": 1,
    "paragraphIds": ["para-2-ghi789-xyz", "para-3-jkl012-xyz", "para-4-mno345-xyz"],
    "summary": "详细分析核心概念的应用场景和实践方法"
  }
]

注意：
- id应该是英文，使用连字符分隔
- title应该是总结性的，体现多个段落的共同主题
- paragraphIds包含对应的段落ID列表
- summary是对该大纲项内容的简要总结
- level范围是1-4，表示标题层级
- 确保返回的是有效的JSON数组`

    const response = await openai.chat.completions.create({
      model: process.env.OUTLINE_GENERATION_MODEL || 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: `请为以下已编码内容生成总结性大纲：

**标题：** ${title}

**编码内容：**
${formattedContent}

请返回JSON格式的大纲结构，确保paragraphIds字段包含相关段落的ID。`
        }
      ],
      max_tokens: parseInt(process.env.OUTLINE_MAX_TOKENS || '1500'),
      temperature: parseFloat(process.env.OUTLINE_TEMPERATURE || '0.3')
    })

    const generatedOutline = response.choices[0]?.message?.content?.trim() || ''

    try {
      // 尝试解析JSON
      const outlineData = JSON.parse(generatedOutline)

      // 验证数据格式
      if (Array.isArray(outlineData)) {
        return outlineData.map((item, index) => ({
          id: item.id || `heading-${index}`,
          title: item.title || `标题 ${index + 1}`,
          level: Math.max(1, Math.min(4, item.level || 1)),
          paragraphIds: item.paragraphIds || [],
          summary: item.summary || item.title || ''
        }))
      }
    } catch (parseError) {
      console.warn('解析LLM生成的大纲JSON失败，使用备用方案:', parseError)
    }

    // 如果JSON解析失败，尝试从文本中提取大纲
    return parseOutlineFromText(generatedOutline)

  } catch (error) {
    console.error('生成智能大纲失败:', error)
    return generateFallbackOutline(content)
  }
}

/**
 * 从文本中解析大纲结构
 */
function parseOutlineFromText(text: string): Array<{id: string, title: string, level: number}> {
  const lines = text.split('\n')
  const outline: Array<{id: string, title: string, level: number}> = []
  
  lines.forEach((line, index) => {
    const trimmedLine = line.trim()
    
    // 检测数字编号格式 (1. 2. 3.)
    const numberMatch = trimmedLine.match(/^(\d+)\.\s*(.+)$/)
    if (numberMatch) {
      const title = numberMatch[2].trim()
      const id = title.toLowerCase()
        .replace(/[^\w\s\u4e00-\u9fff-]/g, '') // 保留中文字符
        .replace(/[\s（）()]+/g, '-') // 替换空格和括号为连字符
        .replace(/-+/g, '-') // 合并多个连字符
        .replace(/^-|-$/g, '') // 移除开头和结尾的连字符
        .substring(0, 50)
      outline.push({ id: id || `heading-${index}`, title, level: 1 })
      return
    }
    
    // 检测缩进格式
    const indentMatch = trimmedLine.match(/^(\s*)-\s*(.+)$/)
    if (indentMatch) {
      const indentLevel = Math.floor(indentMatch[1].length / 2) + 1
      const title = indentMatch[2].trim()
      const id = title.toLowerCase()
        .replace(/[^\w\s\u4e00-\u9fff-]/g, '') // 保留中文字符
        .replace(/[\s（）()]+/g, '-') // 替换空格和括号为连字符
        .replace(/-+/g, '-') // 合并多个连字符
        .replace(/^-|-$/g, '') // 移除开头和结尾的连字符
        .substring(0, 50)
      outline.push({ 
        id: id || `heading-${index}`, 
        title, 
        level: Math.max(1, Math.min(4, indentLevel))
      })
      return
    }
    
    // 检测简单的标题行
    if (trimmedLine.length > 3 && trimmedLine.length < 100 && !trimmedLine.includes('：') && !trimmedLine.includes(':')) {
      const title = trimmedLine
      const id = title.toLowerCase()
        .replace(/[^\w\s\u4e00-\u9fff-]/g, '') // 保留中文字符
        .replace(/[\s（）()]+/g, '-') // 替换空格和括号为连字符
        .replace(/-+/g, '-') // 合并多个连字符
        .replace(/^-|-$/g, '') // 移除开头和结尾的连字符
        .substring(0, 50)
      outline.push({ id: id || `heading-${index}`, title, level: 1 })
    }
  })
  
  return outline
}

/**
 * 生成备用大纲（基于简单文本解析）
 */
function generateFallbackOutline(content: string): Array<{id: string, title: string, level: number}> {
  const lines = content.split('\n')
  const headings: Array<{id: string, title: string, level: number}> = []

  lines.forEach((line, index) => {
    // 检测Markdown标题
    const headingMatch = line.match(/^(#{1,6})\s+(.+)$/)
    if (headingMatch) {
      const level = headingMatch[1].length
      const title = headingMatch[2].trim()
      const id = title.toLowerCase()
        .replace(/[^\w\s\u4e00-\u9fff-]/g, '') // 保留中文字符
        .replace(/[\s（）()]+/g, '-') // 替换空格和括号为连字符
        .replace(/-+/g, '-') // 合并多个连字符
        .replace(/^-|-$/g, '') // 移除开头和结尾的连字符
        .substring(0, 50)
      headings.push({ id: id || `heading-${index}`, title, level })
    }
    // 检测其他可能的标题格式
    else if (line.trim() && !line.startsWith(' ') && line.length < 100 && line.length > 3) {
      const nextLine = lines[index + 1]
      if (nextLine && (nextLine.startsWith('=') || nextLine.startsWith('-'))) {
        const title = line.trim()
        const id = title.toLowerCase()
          .replace(/[^\w\s\u4e00-\u9fff-]/g, '') // 保留中文字符
          .replace(/[\s（）()]+/g, '-') // 替换空格和括号为连字符
          .replace(/-+/g, '-') // 合并多个连字符
          .replace(/^-|-$/g, '') // 移除开头和结尾的连字符
          .substring(0, 50)
        headings.push({ id: id || `heading-${index}`, title, level: nextLine.startsWith('=') ? 1 : 2 })
      }
    }
  })

  return headings
}

export async function POST(request: NextRequest) {
  try {
    const { content, title } = await request.json()
    
    if (!content) {
      return NextResponse.json({ error: '缺少内容参数' }, { status: 400 })
    }

    const outline = await generateOutlineForContent(content, title || '未命名文档')
    
    return NextResponse.json({ 
      outline,
      success: true 
    })

  } catch (error) {
    console.error('生成大纲失败:', error)
    return NextResponse.json({ 
      error: '生成大纲失败，请稍后重试',
      outline: []
    }, { status: 500 })
  }
}
